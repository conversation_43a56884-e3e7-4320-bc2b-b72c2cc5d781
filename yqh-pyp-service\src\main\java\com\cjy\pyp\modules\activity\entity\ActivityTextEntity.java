package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
@Data
@TableName("activity_text")
@Accessors(chain = true)
public class ActivityTextEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 会议id
     */
    private Long activityId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;
    /**
     *
     */
    private Integer paixu;
    /**
     * 使用次数
     */
    private Integer useCount;
    /**
     * 使用模型
     */
    private String model;
    /**
     * 提示词
     */
    private String prompt;
    /**
     * 结果摘要
     */
    private String resultSummary;
    /**
     * 搜索结果(JSON格式)
     */
    private String searchResults;
    /**
     * AI思考过程
     */
    private String thinkProcess;
    /**
     * 自定义输入
     */
    private String query;
    /**
     * 标题
     */
    private String name;
    /**
     * 提示词
     */
    private String title;
    /**
     * 文案
     */
    private String result;
    /**
     * 标题生成模式（ai: AI生成, manual: 手动填写）
     */
    private String nameMode;
    /**
     * 广告类型（douyin: 抖音, xiaohongshu: 小红书, weixin: 微信朋友圈等）
     */
    private String adType;
    /**
     * 文案状态（normal: 正常生成, pre_generated: 预生成）
     */
    private String status;
    /**
     * 用户自定义输入内容
     */
    private String userCustomInput;

    /**
     * AI标签，用于生成特定场景的文案（如：男,女,儿童等）
     */
    private String aiTag;

    @TableField(exist = false)
    private String repeatToken;
}
