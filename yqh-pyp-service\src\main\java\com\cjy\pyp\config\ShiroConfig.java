package com.cjy.pyp.config;

import com.cjy.pyp.modules.sys.oauth2.OAuth2Filter;
import com.cjy.pyp.modules.sys.oauth2.OAuth2Realm;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro配置
 * @<NAME_EMAIL>
 */
@Configuration
public class ShiroConfig {

    @Bean("securityManager")
    public SecurityManager securityManager(OAuth2Realm oAuth2Realm) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(oAuth2Realm);
        securityManager.setRememberMeManager(null);
        return securityManager;
    }

    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);

        //oauth过滤
        Map<String, Filter> filters = new HashMap<>(4);
        filters.put("oauth2", new OAuth2Filter());
        shiroFilter.setFilters(filters);

        Map<String, String> filterMap = new LinkedHashMap<>(8);
        filterMap.put("/web/activity/findWechatQrCode/**", "anon"); // 获取微信二维码
        filterMap.put("/web/activity/activityguest/list", "anon"); // 会议嘉宾列表
        filterMap.put("/web/activity/activitybottom/findByActivity/**", "anon"); // 会议嘉宾列表
        filterMap.put("/web/activity/activityguest/getById/**", "anon"); // 会议嘉宾详情
        filterMap.put("/web/activity/activityguest/getTopicAndSchedule/**", "anon"); // 会议嘉宾详情--获取日程
        filterMap.put("/place/placeactivity/findByActivityId/**", "anon"); // 获取场地列表
        filterMap.put("/place/placeactivity/notify", "anon"); // 获取场地列表
        filterMap.put("/place/placeactivitytopic/findByActivityId/**", "anon"); // 获取场地列表
        filterMap.put("/web/activity/activity/list", "anon"); // 会议列表
        filterMap.put("/web/activity/activity/historyList", "anon"); // 会议列表-历史记录
        filterMap.put("/web/place/placeactivitytopic/list", "anon"); // 会议日程列表
        filterMap.put("/activity/activityviewlog/count", "anon"); // 会议统计
        filterMap.put("/activity/activity/info/**", "anon"); // 会议基本信息
        filterMap.put("/activity/activity/dateBetween/**", "anon"); // 日期区间
        filterMap.put("/cms/cms/findByActivityId/**", "anon");// 建站信息
        filterMap.put("/cms/cms/info/**", "anon");// 建站信息
        filterMap.put("/sys/login", "anon");
        filterMap.put("/wxAuth/**", "anon"); // 微信授权
        filterMap.put("/wxMiniAuth/codeToUserInfo", "anon"); // 微信小程序授权
        filterMap.put("/wx/msg/**", "anon"); // 微信消息
        filterMap.put("/web/user/**", "anon"); // 判断是否登陆和登录操作
        filterMap.put("/sms/sms/send", "anon"); // 发送短信开放接口
        filterMap.put("/sms/sms/verificationCode", "anon"); // 效验短信开放接口
        filterMap.put("/web/wxAccount/info", "anon"); // 获取公众号所在ID名称
        filterMap.put("/web/upload", "anon"); // 上传签名
        filterMap.put("/web/activity/activityguest/**", "anon"); // 更新专家劳务费信息
        filterMap.put("/web/activity/activityuserapplyorder/notify", "anon"); // 微信支付回调-报名
        filterMap.put("/web/activity/activityuserapplyorder/notifyAli", "anon"); // 支付宝支付回调-报名
        filterMap.put("/web/activity/activityuserapplyorder/notifyProxy", "anon"); // 代报名微信支付回调-报名
        filterMap.put("/web/activity/activityuserapplyorder/notifyAliProxy", "anon"); // 代报名支付宝支付回调-报名
        filterMap.put("/web/hotel/hotelactivity/notify", "anon"); // 微信支付回调-酒店
        filterMap.put("/web/hotel/hotelactivity/notifyAli", "anon"); // 支付宝支付回调-酒店
        filterMap.put("/web/website/**", "anon"); // 官网游览接口
        filterMap.put("/web/proxy/proxyapply/submitNoLogin", "anon"); // 申请试用，免登录版本
        filterMap.put("/web/apply/applyactivitychannelconfig/info", "anon"); // 报名通道详情-微信通道版本
        filterMap.put("/web/index", "anon"); // 首页数据
        filterMap.put("/web/activity/activityConfig/check", "anon"); // 配置校验
        filterMap.put("/common/isExpire", "anon"); // 是否超过半年
        filterMap.put("/web/config/configairport/findByName", "anon"); // 磐河
        filterMap.put("/web/config/configtrainstation/findByName*", "anon"); // 磐河
        filterMap.put("/panhe/**", "anon"); // 磐河
        filterMap.put("/web/xiaohongshu/**", "anon"); // 小红书
        filterMap.put("/web/kuaishou/**", "anon"); // 快手
        filterMap.put("/web/douyin/**", "anon"); // 抖音
        filterMap.put("/web/activity/recharge/pay/wechatNotify", "anon"); // 抖音
        filterMap.put("/web/activity/recharge/pay/alipayNotify", "anon"); // 抖音
        filterMap.put("/web/activity/review/**", "anon"); // 抖音
        filterMap.put("/web/miniprogram/**", "anon"); // 抖音
        filterMap.put("/web/groupbuying/**", "anon"); // 团购券
        filterMap.put("/web/activity/activitytext/getByTag", "anon");
        filterMap.put("/web/activity/activitytext/getAiTags", "anon");
        filterMap.put("/web/aimodel/**", "anon"); 
        filterMap.put("/**", "oauth2");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        return shiroFilter;
    }

    @Bean("lifecycleBeanPostProcessor")
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

}
