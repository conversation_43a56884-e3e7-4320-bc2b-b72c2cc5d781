# Tomcat
server:
    tomcat:
        uri-encoding: UTF-8
        max-threads: 1000
        min-spare-threads: 30
    port: 8077
    connection-timeout: 5000ms
    servlet:
        context-path: /pyp

debug: false

spring:
    profiles:
        active: dev #此处由maven的环境选择决定，参考：https://www.jianshu.com/p/b7c75b0c364c
    # jackson时间格式化
    servlet:
        multipart:
            max-file-size: 1024MB
            max-request-size: 1024MB
            enabled: true
    mvc:
        throw-exception-if-no-handler-found: true
    task:
        scheduling:
            pool:
                size: 5
    redis:
        open: true  # 是否开启redis缓存  true开启   false关闭
        database: 6
#        host: *************
        host: *************
        port: 6379
        password: <PERSON><PERSON><PERSON><PERSON>@888   # 密码（默认为空）
        timeout: 6000ms  # 连接超时时长（毫秒）
        jedis:
            pool:
                max-idle: 8
                max-active: 8
                max-wait: -1
                min-idle: 0
    rabbitmq:
        host: *************
        port: 5672
        username: guest
        password: guest
#        # 手动确认ack
        listener:
            simple:
                acknowledge-mode: manual
                auto-startup: false
            direct:
                auto-startup: false


#mybatis
mybatis-plus:
    mapper-locations: classpath*:/mapper/**/*.xml
    #实体扫描，多个package用逗号或者分号分隔
    type-aliases-package: com.cjy.pyp.modules.*.entity
    type-handlers-package: com.cjy.pyp.common.handler
    global-config:
        #数据库相关配置
        db-config:
            #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
            id-type: ASSIGN_ID
            logic-delete-value: -1
            logic-not-delete-value: 0
        banner: false
    #原生配置
    configuration:
        map-underscore-to-camel-case: true
        cache-enabled: true
        call-setters-on-nulls: true
        jdbc-type-for-null: 'null'

renren:
    # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
    jwt:
        # 加密秘钥
        secret: kF3`eF0|bD01!eA1#gE0!eE31_hD0@kJ0#[<EMAIL>]
        # token有效时长，7天，单位秒
        expire: 604800
        header: token

wx:
    mp:
        # 自动回复消息发送间隔（毫秒），适当增加间隔可提升用户体验
        autoReplyInterval: 3000

aliyunAccessKey: LTAI5tGGFL5U3sp72Dayk7Pf
aliyunAccessKeySecret: ******************************

#自己的
#vodaliyunAccessKey: LTAIWmfaHHTzUG5L
#vodaliyunAccessKeySecret: ******************************
#pushKey: wNlk44juRcy2d4ap
#playKey: sL78zGfTvQBhpZLq
#pushDomain: push.zhaoshengniuren.com
#playDomain: play.zhaoshengniuren.com
#中天的
vodaliyunAccessKey: LTAI5tGjnezWuA7hnjL6Kbes
vodaliyunAccessKeySecret: ******************************
push_key: iJUJDibeoegOPacH
play_key: jbCS7OJzGzlY3MU8
push_domain: push.ztmeeting.com
play_domain: play.ztmeeting.com

xiaohongshu:
    appKey: red.39AbhJkzELpUA70Z
    appSecret: e5f194242d90ce1fa67ea88af246b19d

kuaishou:
    appKey: ks712131693718308739
    appSecret: lbnhFLmJo8c5SFnzXaMiOg

# 抖音开放平台配置
douyin:
    appKey: awl6b1gjdhhs1g0w
    appSecret: 5dbfdbb3e90a274d83f4f6fa4c6f4f72
    enabled: true                    # 是否启用抖音分享功能
    shareToPublish: 1               # 分享类型：0-分享到编辑页，1-直接分享到发布页
    clientTokenCacheTime: 7200      # client_token缓存时间（秒）
    ticketCacheTime: 7200           # ticket缓存时间（秒）
    requestTimeout: 30000           # API请求超时时间（毫秒）

# AI模型配置已迁移到数据库，请通过管理后台进行配置

# 阿里云智能媒体服务配置
aliyun:
    ice:
        access-key-id: LTAInNP6Bhj6J6zG
        access-key-secret: ******************************
        region-id: cn-shanghai
        endpoint: ice.cn-shanghai.aliyuncs.com
        video-edit-template-id: ""
        subtitle-template-id: ""
        tts-template-id: ""
        oss-bucket: yqhpyp
        oss-prefix: "ice/"

# 视频混剪定时任务配置
video-edit:
    scheduler:
        enabled: true  # 是否启用定时任务
        # check-interval: 300000  # 检查间隔（毫秒），默认5分钟
        check-interval: 60000  # 检查间隔（毫秒），默认1分钟
        timeout-hours: 2  # 任务超时时间（小时）
        cleanup-days: 7   # 清理过期任务的天数

# 功能使用次数消耗配置
usage:
    cost:
        text-generation: 0    # 生成文案消耗次数
        video-generation: 0   # 生成视频消耗次数
        image-generation: 0   # 生成图文成品消耗次数
        forward: 1           # 转发消耗次数（抖音、快手、小红书统一）