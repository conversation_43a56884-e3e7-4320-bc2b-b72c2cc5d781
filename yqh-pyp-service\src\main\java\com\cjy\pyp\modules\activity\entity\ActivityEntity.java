package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.cjy.pyp.modules.activity.vo.OssImageVo;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-06-02 23:19:20
 */
@Data
@TableName("tb_activity")
public class ActivityEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Long id;
	/**
	 * 会议编号
	 */
	private String code;
	/**
	 * 会议名称
	 */
	private String name;
	/**
	 * 开始时间
	 */
	// @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date startTime;
	/**
	 * 结束时间
	 */
	// @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date endTime;
	/**
	 * 省份
	 */
	private Long provinceId;
	/**
	 * 城市
	 */
	private Long cityId;
	/**
	 * 详细地址
	 */
	private String address;
	/**
	 * 会议图片
	 */
	private String pcBanner;
	/**
	 * 手机端图片
	 */
	private String mobileBanner;
	/**
	 * 点击数
	 */
	private Integer pvCount;
	/**
	 * 访问数
	 */
	private Integer uvCount;
	/**
	 * 经度
	 */
	private String longitude;
	/**
	 * 纬度
	 */
	private String latitude;
	/**
	 * 详细介绍
	 */
	private String introduction;
	/**
	 * 报名完关注二维码
	 */
	private String subscribeImg;
	/**
	 * 九宫格背景图
	 */
	private String background;
	/**
	 * 模板
	 */
	private Long templateId;
	/**
	 * 返回图标
	 */
	private String backImg;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 管理员账号
	 */
	private String adminAccount;
	/**
	 * 创建时间
	 */
	// @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	@TableField(fill = FieldFill.INSERT)
	private Date createOn;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long createBy;
	/**
	 * 更新时间
	 */
	// @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	@TableField(fill = FieldFill.UPDATE)
	private Date updateOn;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Long updateBy;
	/**
	 * 活动过期时间
	 */
	private Date expirationTime;
	/**
	 * 是否已过期：0-未过期，1-已过期
	 */
	private Integer isExpired;
	/**
	 * 0-自定义，1-banner广告，2-文件下载
	 */
	private String type;
	/**
	 * 底部颜色
	 */
	private String bottomColor;
	/**
	 * 报名须知
	 */
	private String applyNotify;
	/**
	 * 预订须知
	 */
	private String hotelNotify;
	/**
	 * 跳转第三方链接
	 */
	private String turnurl;
	/**
	 * appid
	 */
	private String appid;

	/**
	 * 所属渠道ID（通过创建者的渠道归属确定）
	 */
	private Long channelId;
	/**
	 * 是否显示报名人数
	 */
	private Integer showApplyNumber;
	/**
	 * 酒店预订是否强制报名
	 */
	private Integer hotelNeedApply;
	/**
	 * 直播是否强制报名
	 */
	private Integer liveNeedApply;
	/**
	 * 背景音乐
	 */
	private String musicUrl;
	/**
	 * 首页广告图
	 */
	private String ad;
	/**
	 * 广告图停留时间
	 */
	private Integer adTime;
	/**
	 * 广告图背景颜色
	 */
	private String adColor;
	/**
	 * 报名成功显示直播
	 */
	private Integer applyLive;
	/**
	 * 报名显示技术支持
	 */
	private Integer applyYhy;
	/**
	 * 是否引导关注
	 */
	private Integer showSub;
	/**
	 * 是否开启代报名
	 */
	private Integer applyProxy;
	/**
	 * 是否跳过微信登录-默认不跳过
	 */
	private Integer isMpLogin;

	private String backUrl;

	private String shareUrl;

	private String fontColor;

	private Integer isFixed;

	/**
	 * 总可用次数（通过充值获得）
	 */
	private Integer allCount;

	/**
	 * 已使用次数
	 */
	private Integer useCount;

	/**
	 * 省份
	 */
	@TableField(exist = false)
	private String provinceName;
	/**
	 * 城市
	 */
	@TableField(exist = false)
	private String cityName;
	@TableField(exist = false)
	private String clientName;
	/**
	 * 公众号轮播图-移动端-处理后用于前端展示
	 */
	@TableField(exist = false)
	public List<OssImageVo> appFileList;
	/**
	 * 公众号轮播图-pc端-处理后用于前端展示
	 */
	@TableField(exist = false)
	public List<OssImageVo> fileList;
	@TableField(exist = false)
	public List<ActivityRoleEntity> activityRoleEntities;

	/**
	 * 是否显示抖音
	 */
	private Integer showDouyin;
	/**
	 * 是否显示小红书
	 */
	private Integer showXiaohongshu;
	/**
	 * 是否显示快手
	 */
	private Integer showKuaishou;
	/**
	 * 是否显示视频号
	 */
	private Integer showShipinhao;
	/**
	 * 是否显示抖音点评
	 */
	private Integer showDouyindianping;
	/**
	 * 是否显示大众点评
	 */
	private Integer showDazhongdianping;
	/**
	 * 是否显示美团点评
	 */
	private Integer showMeituandianping;
	/**
	 * 是否显示企业微信
	 */
	private Integer showQiyeweixin;
	/**
	 * 是否显示wifi
	 */
	private Integer showWifi;
	/**
	 * 是否显示关注快手
	 */
	private Integer showGuanzhukuaishou;
	/**
	 * 是否显示关注抖音
	 */
	private Integer showGuanzhudouyin;
	/**
	 * 是否显示视频点赞
	 */
	private Integer showShipindianzan;
	/**
	 * 是否显示微信小程序
	 */
	private Integer showMiniProgram;
	private String logo;

	/**
	 * 抖音类型，0-视频，1-图文
	 */
	private Integer douyinType;
	
	/**
	 * 小红书类型，0-视频，1-图文
	 */
	private Integer xiaohongshuType;
	
	/**
	 * 抖音POI
	 */
	private String douyinPoi;
	
	/**
	 * 抖音点评
	 */
	private String douyindianping;
	
	/**
	 * 美团
	 */
	private String meituan;
	
	/**
	 * 大众点评
	 */
	private String dazhongdianping;
	
	/**
	 * 企业微信
	 */
	private String qiyeweixin;
	private String zhuyeDouyin;
	private String zhuyeKuaishou;
	/**
	 * 标题生成模式 ai: AI生成, manual: 手动填写
	 */
	private String nameMode;
	/**
	 * 默认标题
	 */
	private String defaultName;
	/**
	 * 默认提示词
	 */
	private String defaultTitle;
	/**
	 * WiFi账号
	 */
	private String wifiAccount;
	/**
	 * WiFi密码
	 */
	private String wifiPassword;
	/**
	 * 公众号图片
	 */
	private String wechatQrCode;
	/**
	 * 用户自定义输入的默认值
	 */
	private String defaultUserInput;

	/**
	 * 是否显示携程点评
	 */
	private Integer showCtrip;

	/**
	 * 携程配置信息
	 */
	private String ctripConfig;


	/**
	 * 是否显示携程点评
	 */
	private Integer showCtripReview;

	/**
	 * 是否显示携程笔记
	 */
	private Integer showCtripNotes;


	/**
	 * 携程点评配置信息
	 */
	private String ctripReviewConfig;

	/**
	 * 携程笔记配置信息
	 */
	private String ctripNotesConfig;

	/**
	 * 是否显示微信公众号二维码
	 */
	private Integer showWechatQr;

	/**
	 * 我的小店类型：0-网页，1-小程序
	 */
	private Integer shopType;

	/**
	 * 我的小店网页URL
	 */
	private String shopUrl;

	/**
	 * 我的小店小程序AppID
	 */
	private String shopAppid;

	/**
	 * 我的小店小程序页面路径
	 */
	private String shopPagePath;

	/**
	 * 小店描述
	 */
	private String shopDescription;

	/**
	 * 是否显示我的小店：0-不显示，1-显示
	 */
	private Integer showMyShop;

	/**
	 * 是否显示团购券：0-不显示，1-显示
	 */
	private Integer showGroupBuying;
	/**
	 * AI标签
	 */
	private String aiTag;
}
